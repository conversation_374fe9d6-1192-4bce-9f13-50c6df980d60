import React, { useState } from "react";
import {
	View,
	TouchableOpacity,
	Text,
	StyleSheet,
	Modal,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

interface LanguageSelectorProps {
	currentLanguage: string;
	onLanguageChange: (language: string) => void;
	style?: any;
}

const LanguageSelector = ({
	currentLanguage,
	onLanguageChange,
}: LanguageSelectorProps) => {
	// State for dialog visibility and focus management
	const [showDialog, setShowDialog] = useState(false);
	const [isButtonFocused, setIsButtonFocused] = useState(false);
	const [isOkFocused, setIsOkFocused] = useState(true); // OK button focused by default
	const [isCancelFocused, setIsCancelFocused] = useState(false);

	// Get the target language (opposite of current)
	const targetLanguage = currentLanguage === "en" ? "fr" : "en";

	// Dialog messages based on current language
	const dialogMessages = {
		en: {
			message:
				"Do you want to change language and content to French?",
			ok: "OK",
			cancel: "CANCEL",
		},
		fr: {
			message:
				"Voulez-vous changer la langue et le contenu en Anglais?",
			ok: "OK",
			cancel: "CANCEL",
		},
	};

	// Handle language toggle button press
	const handleTogglePress = () => {
		setShowDialog(true);
		setIsOkFocused(true); // Focus OK button by default
		setIsCancelFocused(false);
	};

	// Handle dialog confirmation
	const handleConfirm = () => {
		onLanguageChange(targetLanguage);
		setShowDialog(false);
	};

	// Handle dialog cancellation
	const handleCancel = () => {
		setShowDialog(false);
	};

	return (
		<>
			<TouchableOpacity
				style={[
					styles.toggleButton,
					isButtonFocused && styles.toggleButtonFocused,
				]}
				onPress={handleTogglePress}
				onFocus={() => setIsButtonFocused(true)}
				onBlur={() => setIsButtonFocused(false)}
				activeOpacity={1}
			>
				<Text
					style={[
						styles.toggleText,
						isButtonFocused && styles.toggleTextFocused,
					]}
				>
					{currentLanguage.toUpperCase()}
				</Text>
			</TouchableOpacity>

			{/* Language Change Confirmation Dialog */}
			<Modal
				visible={showDialog}
				transparent={true}
				animationType="fade"
				onRequestClose={handleCancel}
			>
				<View style={styles.modalOverlay}>
					<View style={styles.dialogContainer}>
						<Text style={styles.dialogMessage}>
							{
								dialogMessages[
									currentLanguage as keyof typeof dialogMessages
								].message
							}
						</Text>
						<View style={styles.dialogButtons}>
							<TouchableOpacity
								style={[
									styles.dialogButton,
									styles.okButton,
									isOkFocused && styles.dialogButtonFocused,
								]}
								onPress={handleConfirm}
								onFocus={() => {
									setIsOkFocused(true);
									setIsCancelFocused(false);
								}}
								onBlur={() => setIsOkFocused(false)}
								activeOpacity={1}
								autoFocus={true} // Auto-focus OK button when dialog opens
							>
								<Text
									style={[
										styles.dialogButtonText,
										isOkFocused && styles.dialogButtonTextFocused,
									]}
								>
									{
										dialogMessages[
											currentLanguage as keyof typeof dialogMessages
										].ok
									}
								</Text>
							</TouchableOpacity>
							<TouchableOpacity
								style={[
									styles.dialogButton,
									styles.cancelButton,
									isCancelFocused && styles.dialogButtonFocused,
								]}
								onPress={handleCancel}
								onFocus={() => {
									setIsCancelFocused(true);
									setIsOkFocused(false);
								}}
								onBlur={() => setIsCancelFocused(false)}
								activeOpacity={1}
							>
								<Text
									style={[
										styles.dialogButtonText,
										isCancelFocused && styles.dialogButtonTextFocused,
									]}
								>
									{
										dialogMessages[
											currentLanguage as keyof typeof dialogMessages
										].cancel
									}
								</Text>
							</TouchableOpacity>
						</View>
					</View>
				</View>
			</Modal>
		</>
	);
};

const styles = StyleSheet.create({
	/**
	 * Language Toggle Button Styles
	 */
	toggleButton: {
		paddingVertical: scale(8),
		paddingHorizontal: scale(16),
		borderRadius: scale(4),
		borderWidth: scale(2),
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		backgroundColor: "transparent",
		opacity: 0.7,
		minWidth: scale(60), // Ensure consistent button width
	},
	toggleButtonFocused: {
		backgroundColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		borderColor: "#f9f9f9",
		opacity: 1,
		transform: [{ scale: 1.05 }],
	},
	toggleText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		fontWeight: "600",
		textAlign: "center",
	},
	toggleTextFocused: {
		color: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		fontWeight: "bold",
	},

	/**
	 * Dialog Modal Styles
	 */
	modalOverlay: {
		flex: 1,
		backgroundColor: "rgba(0, 0, 0, 0.8)", // Semi-transparent dark overlay
		justifyContent: "center",
		alignItems: "center",
	},
	dialogContainer: {
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		borderRadius: scale(12),
		padding: scale(32),
		minWidth: scale(600),
		maxWidth: scale(800),
		borderWidth: scale(2),
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		shadowColor: "#000",
		shadowOffset: {
			width: 0,
			height: 4,
		},
		shadowOpacity: 0.3,
		shadowRadius: 8,
		elevation: 10, // Android shadow
	},
	dialogMessage: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(28),
		fontWeight: "500",
		textAlign: "center",
		marginBottom: scale(32),
		lineHeight: scale(36),
	},
	dialogButtons: {
		flexDirection: "row",
		justifyContent: "center",
		gap: scale(24),
	},
	dialogButton: {
		paddingVertical: scale(12),
		paddingHorizontal: scale(32),
		borderRadius: scale(8),
		borderWidth: scale(2),
		minWidth: scale(120),
	},
	okButton: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderColor: GLOBAL_STYLES.COLORS.ACCENT,
	},
	cancelButton: {
		backgroundColor: "transparent",
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
	},
	dialogButtonFocused: {
		borderColor: "#f9f9f9",
		transform: [{ scale: 1.05 }],
		shadowColor: "#fff",
		shadowOffset: {
			width: 0,
			height: 0,
		},
		shadowOpacity: 0.5,
		shadowRadius: 4,
		elevation: 5,
	},
	dialogButtonText: {
		fontSize: scale(24),
		fontWeight: "600",
		textAlign: "center",
	},
	dialogButtonTextFocused: {
		color: "#ffffff",
		fontWeight: "bold",
	},
});

export default LanguageSelector;
